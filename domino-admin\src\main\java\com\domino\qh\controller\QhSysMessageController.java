package com.domino.qh.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.domino.common.annotation.Log;
import com.domino.common.constant.ChatConstants;
import com.domino.common.core.controller.BaseController;
import com.domino.common.core.domain.AjaxResult;
import com.domino.common.core.domain.entity.SysUser;
import com.domino.common.core.page.TableDataInfo;
import com.domino.common.enums.BusinessType;
import com.domino.common.minio.MinioConfig;
import com.domino.common.minio.MinioUtil;
import com.domino.common.utils.SecurityUtils;
import com.domino.common.utils.StringUtils;
import com.domino.common.utils.uuid.IdUtils;
import com.domino.common.qh.domain.SysMessage;
import com.domino.system.service.ISysMessageService;
import com.domino.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 消息管理接口
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@RestController
@Slf4j
@RequestMapping("/system/message")
public class QhSysMessageController extends BaseController {

    @Resource
    private MinioConfig minioConfig;

    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private ISysMessageService messageService;
    
    /**
     * 发送消息
     * 请求参数示例:
     * {
     *   "title": "消息标题",
     *   "content": "消息内容",
     *   "type": "SYSTEM",
     *   "receiveScope": "ALL/ORG/USER",
     *   "receiveTarget": "机构ID/用户ID列表(逗号分隔)"
     * }
     */
    @PostMapping("/send")
    @Log(title = "发送消息", businessType = BusinessType.INSERT)
    public AjaxResult sendMessage(@RequestBody SysMessage message) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        return messageService.sendMessage(message, user);
    }
    
    /**
     * 消息列表
     */
    @PostMapping("/page/list")
    public AjaxResult pageList(@RequestBody JSONObject params) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        int pageNum = params.getIntValue("current");
        int pageSize = params.getIntValue("pageSize");
        String type = params.getString("type"); //前端传递的类型非后端类型 all 全部通知 system 系统通知 unread 未读通知
        Integer readStatus = params.getInteger("readStatus");
        if(StringUtils.isEmpty(type)) {
            type = "all";
        }
        if("unread".equals(type)) {
            type = "all";
            readStatus = 0; //标记只要未读的消息
        }
        String keyword = params.getString("keyword");
        String userOid = params.getString("userOid");//获取与谁的聊天记录，仅针对聊天信息有用

        return messageService.pageMessages(pageNum, pageSize, type.toUpperCase(), keyword, readStatus, userOid, user);
    }

    /**
     * 聊天消息列表
     */
    @PostMapping("/chat/page")
    public AjaxResult chatPage(@RequestBody JSONObject params) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        int pageNum = params.getIntValue("current");
        int pageSize = params.getIntValue("pageSize");
        String userOid = params.getString("userOid");//获取与谁的聊天记录
        String keyword = params.getString("keyword");

        return messageService.pageMessages(pageNum, pageSize, ChatConstants.MSG_TYPE_CHAT, keyword, null, userOid, user);
    }
    
    /**
     * 标记消息为已读
     */
    @PostMapping("/read")
    @Log(title = "标记消息已读", businessType = BusinessType.UPDATE)
    public AjaxResult markAsRead(@RequestBody JSONObject params) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String messageIdsStr = params.getString("messageIds");
        if (StringUtils.isEmpty(messageIdsStr)) {
            return AjaxResult.error("消息ID不能为空");
        }
        List<String> messageIds = Arrays.asList(messageIdsStr.split(","));
        return messageService.markAsRead(messageIds, user);
    }
    
    /**
     * 一键标记所有消息为已读
     */
    @PostMapping("/read/all")
    @Log(title = "一键标记所有消息已读", businessType = BusinessType.UPDATE)
    public AjaxResult markAllAsRead(@RequestBody JSONObject params) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String type = params.getString("type");
        String userOid = params.getString("userOid");
        return messageService.markAllAsRead(type, userOid, user);
    }
    
    /**
     * 获取未读消息数量
     */
    @GetMapping("/unread/count")
    public AjaxResult getUnreadCount() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        return messageService.getUnreadCount(user);
    }

    /**
     * 删除聊天内容
     */
    @Log(title = "删除聊天内容", businessType = BusinessType.DELETE)
    @PostMapping("/del/chat")
    public AjaxResult delChatMessage(@RequestBody JSONObject params) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String userOid = params.getString("userOid");
        return messageService.deleteChatMessage(user, userOid);
    }

    /**
     * 发送聊天消息
     */
    @PostMapping("/chat/send")
    @Log(title = "发送聊天消息", businessType = BusinessType.INSERT)
    public AjaxResult sendChatMessage(@RequestBody JSONObject params) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        
        SysMessage message = new SysMessage();
        message.setType(ChatConstants.MSG_TYPE_CHAT);
        message.setContent(params.getString("content"));
        message.setReceiveTarget(params.getString("receiveTarget"));
        message.setIsImage(params.getInteger("isImage"));
        message.setQuoteOid(params.getString("quoteOid"));
        message.setQuoteContent(params.getString("quoteContent"));
        
        return messageService.sendMessage(message, user);
    }

    /**
     * 聊天图片上传
     */
    @Log(title = "聊天图片上传", businessType = BusinessType.IMPORT)
    @PostMapping("/upload/image")
    public AjaxResult uploadChatImage(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return AjaxResult.error("上传文件不能为空");
            }

            // 验证文件类型
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !isImageFile(originalFilename)) {
                return AjaxResult.error("只支持图片文件上传（jpg、jpeg、png、gif）");
            }

            // 验证文件大小（限制为5MB）
            long maxSize = 5 * 1024 * 1024; // 5MB
            if (file.getSize() > maxSize) {
                return AjaxResult.error("图片文件大小不能超过5MB");
            }

            SysUser user = SecurityUtils.getLoginUser().getUser();

            // 生成文件名：chat/年月/用户ID_时间戳_随机ID.扩展名
            String dateFolder = new SimpleDateFormat("yyyyMM").format(new Date());
            String fileExtension = getFileExtension(originalFilename);
            String fileName = String.format("chat/%s/%s_%d_%s.%s",
                    dateFolder,
                    user.getUserId(),
                    System.currentTimeMillis(),
                    IdUtils.fastSimpleUUID().substring(0, 8),
                    fileExtension);

            // 上传到MinIO
            MinioUtil.uploadFile(minioConfig.getBucketName(), fileName, file.getInputStream());

            // 生成访问URL
            String imageUrl = minioConfig.getFileHost() + "/" + minioConfig.getBucketName() + "/" + fileName;

            log.info("用户 {} 上传聊天图片成功: {}", user.getUserName(), fileName);

            AjaxResult result = AjaxResult.success("图片上传成功");
            result.put("url", imageUrl);
            result.put("fileName", fileName);
            result.put("originalName", originalFilename);
            result.put("size", file.getSize());

            return result;

        } catch (Exception e) {
            log.error("聊天图片上传失败: {}", e.getMessage(), e);
            return AjaxResult.error("图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 聊天文件上传（非图片）
     */
    @Log(title = "聊天文件上传", businessType = BusinessType.IMPORT)
    @PostMapping("/upload/file")
    public AjaxResult uploadChatFile(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return AjaxResult.error("上传文件不能为空");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null) {
                return AjaxResult.error("文件名不能为空");
            }

            // 验证文件大小（限制为10MB）
            long maxSize = 10 * 1024 * 1024; // 10MB
            if (file.getSize() > maxSize) {
                return AjaxResult.error("文件大小不能超过10MB");
            }

            // 验证文件类型（排除可执行文件）
            if (isDangerousFile(originalFilename)) {
                return AjaxResult.error("不支持该文件类型");
            }

            SysUser user = SecurityUtils.getLoginUser().getUser();

            // 生成文件名：chat/files/年月/用户ID_时间戳_原文件名
            String dateFolder = new SimpleDateFormat("yyyyMM").format(new Date());
            String safeFileName = sanitizeFileName(originalFilename);
            String fileName = String.format("chat/files/%s/%s_%d_%s",
                    dateFolder,
                    user.getUserId(),
                    System.currentTimeMillis(),
                    safeFileName);

            // 上传到MinIO
            MinioUtil.uploadFile(minioConfig.getBucketName(), fileName, file.getInputStream());

            // 生成访问URL
            String fileUrl = minioConfig.getFileHost() + "/" + minioConfig.getBucketName() + "/" + fileName;

            log.info("用户 {} 上传聊天文件成功: {}", user.getUserName(), fileName);

            AjaxResult result = AjaxResult.success("文件上传成功");
            result.put("url", fileUrl);
            result.put("fileName", fileName);
            result.put("originalName", originalFilename);
            result.put("size", file.getSize());
            result.put("type", getFileType(originalFilename));

            return result;

        } catch (Exception e) {
            log.error("聊天文件上传失败: {}", e.getMessage(), e);
            return AjaxResult.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除聊天文件
     */
    @Log(title = "删除聊天文件", businessType = BusinessType.DELETE)
    @DeleteMapping("/file")
    public AjaxResult deleteChatFile(@RequestParam String fileName) {
        try {
            SysUser user = SecurityUtils.getLoginUser().getUser();

            // 验证文件是否属于当前用户（简单的安全检查）
            if (!fileName.contains("chat/") || !fileName.contains(user.getUserId().toString())) {
                return AjaxResult.error("无权限删除该文件");
            }

            // 从MinIO删除文件
            MinioUtil.removeFile(minioConfig.getBucketName(), fileName);

            log.info("用户 {} 删除聊天文件成功: {}", user.getUserName(), fileName);

            return AjaxResult.success("文件删除成功");

        } catch (Exception e) {
            log.error("删除聊天文件失败: {}", e.getMessage(), e);
            return AjaxResult.error("文件删除失败: " + e.getMessage());
        }
    }

    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        return extension.matches("jpg|jpeg|png|gif|bmp|webp");
    }

    /**
     * 判断是否为危险文件类型
     */
    private boolean isDangerousFile(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        String[] dangerousExtensions = {"exe", "bat", "cmd", "com", "pif", "scr", "vbs", "js", "jar", "sh"};
        for (String dangerous : dangerousExtensions) {
            if (extension.equals(dangerous)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1);
        }
        return "";
    }

    /**
     * 获取文件类型
     */
    private String getFileType(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        if (isImageFile(fileName)) {
            return "image";
        } else if (extension.matches("pdf")) {
            return "pdf";
        } else if (extension.matches("doc|docx")) {
            return "document";
        } else if (extension.matches("xls|xlsx")) {
            return "spreadsheet";
        } else if (extension.matches("ppt|pptx")) {
            return "presentation";
        } else if (extension.matches("txt|md")) {
            return "text";
        } else if (extension.matches("zip|rar|7z")) {
            return "archive";
        } else {
            return "file";
        }
    }

    /**
     * 清理文件名，移除特殊字符
     */
    private String sanitizeFileName(String fileName) {
        // 移除或替换特殊字符，保留中文、英文、数字、点号、下划线、连字符
        return fileName.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9._-]", "_");
    }

    /**
     * WebSocket连接状态检查
     * 用于前端检查WebSocket服务是否可用
     */
    @GetMapping("/status")
    public AjaxResult getWebSocketStatus() {
        return AjaxResult.success("WebSocket服务正常", true);
    }

    /**
     * 获取WebSocket连接信息
     * 返回WebSocket连接所需的基本信息
     */
    @GetMapping("/info")
    public AjaxResult getWebSocketInfo() {
        return AjaxResult.success("WebSocket连接信息",
                "请使用 ws://localhost:8080/websocket/{token} 进行连接，其中token为用户登录token");
    }

    /**
     * 获取聊天用户列表
     */
    @PostMapping("/chat/list")
    public AjaxResult chatList(@RequestBody JSONObject params) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String start = "1";
        String limit = "100";// 最大只能获取100个用户列表
        int pageNum = Integer.parseInt(start);
        int pageSize = Integer.parseInt(limit);

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("keyWord", params.getString("keyWord"));

        IPage<SysUser> userPage = userService.getChatPage(pageNum, pageSize, queryParams, user);

        TableDataInfo dataTable = new TableDataInfo();
        dataTable.setCode(200);
        dataTable.setMsg("查询成功");
        dataTable.setRows(userPage.getRecords());
        dataTable.setTotal(userPage.getTotal());

        return AjaxResult.success(dataTable);
    }

    /**
     * 获取机构在线用户列表
     */
    @GetMapping("/online/{orgs}")
    public AjaxResult getOnlineUsers(@PathVariable String orgs) {
        return AjaxResult.success(userService.getOrgOnlineUser(orgs));
    }
}
