<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.domino.system.mapper.SysMessageMapper">

    <resultMap type="com.domino.common.qh.domain.SysMessage" id="SysMessageResult">
        <id property="messageId" column="message_id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="type" column="type"/>
        <result property="senderId" column="sender_id"/>
        <result property="senderName" column="sender_name"/>
        <result property="orgId" column="org_id"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="receiveScope" column="receive_scope"/>
        <result property="receiveTarget" column="receive_target"/>
        <result property="readStatus" column="read_status"/>
        <result property="senderChatSex" column="sender_chat_sex"/>
        <result property="senderChatPhone" column="sender_chat_phone"/>
        <result property="receiveTargetChatSex" column="receive_target_chat_sex"/>
        <result property="receiveTargetChatPhone" column="receive_target_chat_phone"/>
        <result property="isImage" column="is_image"/>
        <result property="quoteOid" column="quote_oid"/>
        <result property="quoteContent" column="quote_content"/>
        <result property="quoteTimestamp" column="quote_timestamp"/>
        <result property="chatReadStatus" column="chat_read_status"/>
    </resultMap>

    <select id="pageMessages" resultMap="SysMessageResult">
        select a.* from (
            select a.*, if(b.receiver_id is null, 0, 1) as read_status from sys_message a
                 left join sys_message_receiver b on a.message_id = b.message_id and b.user_id = #{user.userId}
            <where>
                <choose>
                    <when test="type != null and type != '' and type != 'ALL'">
                        a.type = #{type}
                    </when>
                    <otherwise>
                        (a.type = 'SYSTEM' or a.type = 'PERSONAL')
                    </otherwise>
                </choose>
                <if test="keyword != null and keyword != ''">
                and (a.title like concat('%', #{keyword},'%') or a.content like concat('%', #{keyword},'%'))
                </if>
                <choose>
                    <when test="type == 'CHAT'">
                        and a.receive_scope = 'USER' and (
                               a.receive_target = #{userOid} and a.sender_id = #{user.userId}
                            or a.receive_target = #{user.userId} and a.sender_id = #{userOid}
                        )
                    </when>
                    <otherwise>
                        and (a.receive_scope = 'ALL'
                            or (a.receive_scope = 'ORG' and a.receive_target like concat('%', #{user.deptId},'%'))
                            or (a.receive_scope = 'USER' and a.receive_target = #{user.userId})
                        )
                    </otherwise>
                </choose>
            </where>
        ) a
        <where>
            <if test="readStatus != null and readStatus != -1">
                read_status = #{readStatus}
            </if>
            and del_flag = 0
        </where>
        order by create_time desc
    </select>

    <select id="getUnreadCount" resultType="java.util.Map">
        select type, sum(read_status) nums, sum(chat_read_status) chatNums from (
            SELECT if(a.type = 'PERSONAL', 'SYSTEM', type) AS type
            , if(b.receiver_id IS NULL, 1, 0) AS read_status
            , if(a.chat_read_status = 0, 1, 0) as chat_read_status from sys_message a
            left join sys_message_receiver b on a.message_id = b.message_id and b.user_id = #{user.userId}
                where (
                    a.receive_scope = 'ALL'
                    or (a.receive_scope = 'ORG' and a.receive_target like concat('%', #{user.deptId},'%'))
                    or (a.receive_scope = 'USER' and a.receive_target = #{user.userId})
                )
                and del_flag = 0
            ) a
        group by type
    </select>

    <!-- ==================== 基础增删改查方法 ==================== -->

    <!-- 插入消息 -->
    <insert id="insertMessage" parameterType="com.domino.common.qh.domain.SysMessage">
        insert into sys_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="messageId != null and messageId != ''">message_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="senderId != null">sender_id,</if>
            <if test="senderName != null and senderName != ''">sender_name,</if>
            <if test="orgId != null">org_id,</if>
            <if test="receiveScope != null and receiveScope != ''">receive_scope,</if>
            <if test="receiveTarget != null and receiveTarget != ''">receive_target,</if>
            <if test="senderChatSex != null and senderChatSex != ''">sender_chat_sex,</if>
            <if test="senderChatPhone != null and senderChatPhone != ''">sender_chat_phone,</if>
            <if test="receiveTargetChatSex != null and receiveTargetChatSex != ''">receive_target_chat_sex,</if>
            <if test="receiveTargetChatPhone != null and receiveTargetChatPhone != ''">receive_target_chat_phone,</if>
            <if test="isImage != null">is_image,</if>
            <if test="quoteOid != null and quoteOid != ''">quote_oid,</if>
            <if test="quoteContent != null and quoteContent != ''">quote_content,</if>
            <if test="quoteTimestamp != null">quote_timestamp,</if>
            <if test="chatReadStatus != null">chat_read_status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            del_flag
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="messageId != null and messageId != ''">#{messageId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="senderId != null">#{senderId},</if>
            <if test="senderName != null and senderName != ''">#{senderName},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="receiveScope != null and receiveScope != ''">#{receiveScope},</if>
            <if test="receiveTarget != null and receiveTarget != ''">#{receiveTarget},</if>
            <if test="senderChatSex != null and senderChatSex != ''">#{senderChatSex},</if>
            <if test="senderChatPhone != null and senderChatPhone != ''">#{senderChatPhone},</if>
            <if test="receiveTargetChatSex != null and receiveTargetChatSex != ''">#{receiveTargetChatSex},</if>
            <if test="receiveTargetChatPhone != null and receiveTargetChatPhone != ''">#{receiveTargetChatPhone},</if>
            <if test="isImage != null">#{isImage},</if>
            <if test="quoteOid != null and quoteOid != ''">#{quoteOid},</if>
            <if test="quoteContent != null and quoteContent != ''">#{quoteContent},</if>
            <if test="quoteTimestamp != null">#{quoteTimestamp},</if>
            <if test="chatReadStatus != null">#{chatReadStatus},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            0
        </trim>
    </insert>

    <!-- 批量插入消息 -->
    <insert id="batchInsertMessage" parameterType="java.util.List">
        insert into sys_message (
            message_id, title, content, type, sender_id, sender_name, org_id,
            receive_scope, receive_target, sender_chat_sex, sender_chat_phone,
            receive_target_chat_sex, receive_target_chat_phone, is_image,
            quote_oid, quote_content, quote_timestamp, chat_read_status,
            create_by, create_time, update_by, update_time, remark, del_flag
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.messageId}, #{item.title}, #{item.content}, #{item.type},
                #{item.senderId}, #{item.senderName}, #{item.orgId},
                #{item.receiveScope}, #{item.receiveTarget}, #{item.senderChatSex}, #{item.senderChatPhone},
                #{item.receiveTargetChatSex}, #{item.receiveTargetChatPhone}, #{item.isImage},
                #{item.quoteOid}, #{item.quoteContent}, #{item.quoteTimestamp}, #{item.chatReadStatus},
                #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.remark}, 0
            )
        </foreach>
    </insert>

    <!-- 根据ID查询消息 -->
    <select id="selectMessageById" parameterType="String" resultMap="SysMessageResult">
        select message_id, title, content, type, sender_id, sender_name, org_id,
               receive_scope, receive_target, sender_chat_sex, sender_chat_phone,
               receive_target_chat_sex, receive_target_chat_phone, is_image,
               quote_oid, quote_content, quote_timestamp, chat_read_status,
               create_by, create_time, update_by, update_time, remark, del_flag
        from sys_message
        where message_id = #{messageId} and del_flag = 0
    </select>

    <!-- 查询消息列表 -->
    <select id="selectMessageList" parameterType="com.domino.common.qh.domain.SysMessage" resultMap="SysMessageResult">
        select message_id, title, content, type, sender_id, sender_name, org_id,
               receive_scope, receive_target, sender_chat_sex, sender_chat_phone,
               receive_target_chat_sex, receive_target_chat_phone, is_image,
               quote_oid, quote_content, quote_timestamp, chat_read_status,
               create_by, create_time, update_by, update_time, remark, del_flag
        from sys_message
        <where>
            del_flag = 0
            <if test="title != null and title != ''">
                and title like concat('%', #{title}, '%')
            </if>
            <if test="content != null and content != ''">
                and content like concat('%', #{content}, '%')
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="senderId != null">
                and sender_id = #{senderId}
            </if>
            <if test="senderName != null and senderName != ''">
                and sender_name like concat('%', #{senderName}, '%')
            </if>
            <if test="receiveScope != null and receiveScope != ''">
                and receive_scope = #{receiveScope}
            </if>
            <if test="receiveTarget != null and receiveTarget != ''">
                and receive_target = #{receiveTarget}
            </if>
            <if test="isImage != null">
                and is_image = #{isImage}
            </if>
            <if test="chatReadStatus != null">
                and chat_read_status = #{chatReadStatus}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''">
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>

    <!-- 更新消息 -->
    <update id="updateMessage" parameterType="com.domino.common.qh.domain.SysMessage">
        update sys_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="senderId != null">sender_id = #{senderId},</if>
            <if test="senderName != null and senderName != ''">sender_name = #{senderName},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="receiveScope != null and receiveScope != ''">receive_scope = #{receiveScope},</if>
            <if test="receiveTarget != null and receiveTarget != ''">receive_target = #{receiveTarget},</if>
            <if test="senderChatSex != null and senderChatSex != ''">sender_chat_sex = #{senderChatSex},</if>
            <if test="senderChatPhone != null and senderChatPhone != ''">sender_chat_phone = #{senderChatPhone},</if>
            <if test="receiveTargetChatSex != null and receiveTargetChatSex != ''">receive_target_chat_sex = #{receiveTargetChatSex},</if>
            <if test="receiveTargetChatPhone != null and receiveTargetChatPhone != ''">receive_target_chat_phone = #{receiveTargetChatPhone},</if>
            <if test="isImage != null">is_image = #{isImage},</if>
            <if test="quoteOid != null and quoteOid != ''">quote_oid = #{quoteOid},</if>
            <if test="quoteContent != null and quoteContent != ''">quote_content = #{quoteContent},</if>
            <if test="quoteTimestamp != null">quote_timestamp = #{quoteTimestamp},</if>
            <if test="chatReadStatus != null">chat_read_status = #{chatReadStatus},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where message_id = #{messageId}
    </update>

    <!-- 批量更新消息已读状态 -->
    <update id="batchUpdateReadStatus">
        update sys_message set chat_read_status = #{readStatus}
        where message_id in
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
    </update>

    <!-- 更新聊天消息已读状态 -->
    <update id="updateChatReadStatus">
        update sys_message set chat_read_status = 1
        where type = 'CHAT'
        and ((sender_id = #{senderId} and receive_target = #{receiveTarget})
             or (sender_id = #{receiveTarget} and receive_target = #{senderId}))
        and chat_read_status = 0
    </update>

    <!-- 逻辑删除消息 -->
    <update id="deleteMessageById" parameterType="String">
        update sys_message set del_flag = 1 where message_id = #{messageId}
    </update>

    <!-- 批量逻辑删除消息 -->
    <update id="deleteMessageByIds" parameterType="String">
        update sys_message set del_flag = 1 where message_id in
        <foreach collection="array" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
    </update>

    <!-- 物理删除消息 -->
    <delete id="removeMessageById" parameterType="String">
        delete from sys_message where message_id = #{messageId}
    </delete>

    <!-- 批量物理删除消息 -->
    <delete id="removeMessageByIds" parameterType="String">
        delete from sys_message where message_id in
        <foreach collection="array" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
    </delete>

    <!-- 删除聊天记录 -->
    <update id="deleteChatMessages">
        update sys_message set del_flag = 1
        where type = 'CHAT'
        and ((sender_id = #{senderId} and receive_target = #{receiveTarget})
             or (sender_id = #{receiveTarget} and receive_target = #{senderId}))
    </update>

    <!-- ==================== 扩展查询方法 ==================== -->

    <!-- 根据类型查询消息数量 -->
    <select id="countMessagesByType" parameterType="String" resultType="int">
        select count(*) from sys_message
        where type = #{type} and del_flag = 0
    </select>

    <!-- 根据发送者查询消息列表 -->
    <select id="selectMessagesBySender" parameterType="Long" resultMap="SysMessageResult">
        select message_id, title, content, type, sender_id, sender_name, org_id,
               receive_scope, receive_target, sender_chat_sex, sender_chat_phone,
               receive_target_chat_sex, receive_target_chat_phone, is_image,
               quote_oid, quote_content, quote_timestamp, chat_read_status,
               create_by, create_time, update_by, update_time, remark, del_flag
        from sys_message
        where sender_id = #{senderId} and del_flag = 0
        order by create_time desc
    </select>

    <!-- 根据接收者查询消息列表 -->
    <select id="selectMessagesByReceiver" parameterType="String" resultMap="SysMessageResult">
        select message_id, title, content, type, sender_id, sender_name, org_id,
               receive_scope, receive_target, sender_chat_sex, sender_chat_phone,
               receive_target_chat_sex, receive_target_chat_phone, is_image,
               quote_oid, quote_content, quote_timestamp, chat_read_status,
               create_by, create_time, update_by, update_time, remark, del_flag
        from sys_message
        where receive_target = #{receiveTarget} and del_flag = 0
        order by create_time desc
    </select>

    <!-- 查询聊天消息列表 -->
    <select id="selectChatMessages" resultMap="SysMessageResult">
        select message_id, title, content, type, sender_id, sender_name, org_id,
               receive_scope, receive_target, sender_chat_sex, sender_chat_phone,
               receive_target_chat_sex, receive_target_chat_phone, is_image,
               quote_oid, quote_content, quote_timestamp, chat_read_status,
               create_by, create_time, update_by, update_time, remark, del_flag
        from sys_message
        where type = 'CHAT'
        and ((sender_id = #{senderId} and receive_target = #{receiveTarget})
             or (sender_id = #{receiveTarget} and receive_target = #{senderId}))
        and del_flag = 0
        order by create_time asc
    </select>

    <!-- 查询未读聊天消息数量 -->
    <select id="countUnreadChatMessages" resultType="int">
        select count(*) from sys_message
        where type = 'CHAT'
        and receive_target = #{userId}
        and chat_read_status = 0
        and del_flag = 0
    </select>

    <!-- 查询最近的聊天消息 -->
    <select id="selectLatestChatMessage" resultMap="SysMessageResult">
        select message_id, title, content, type, sender_id, sender_name, org_id,
               receive_scope, receive_target, sender_chat_sex, sender_chat_phone,
               receive_target_chat_sex, receive_target_chat_phone, is_image,
               quote_oid, quote_content, quote_timestamp, chat_read_status,
               create_by, create_time, update_by, update_time, remark, del_flag
        from sys_message
        where type = 'CHAT'
        and ((sender_id = #{senderId} and receive_target = #{receiveTarget})
             or (sender_id = #{receiveTarget} and receive_target = #{senderId}))
        and del_flag = 0
        order by create_time desc
        limit 1
    </select>

    <!-- 清理过期的聊天图片 -->
    <update id="cleanExpiredChatImages">
        update sys_message set del_flag = 1
        where type = 'CHAT'
        and is_image = 1
        and create_time &lt; date_sub(now(), interval #{days} day)
    </update>

    <!-- 根据引用ID查询消息 -->
    <select id="selectMessagesByQuoteId" parameterType="String" resultMap="SysMessageResult">
        select message_id, title, content, type, sender_id, sender_name, org_id,
               receive_scope, receive_target, sender_chat_sex, sender_chat_phone,
               receive_target_chat_sex, receive_target_chat_phone, is_image,
               quote_oid, quote_content, quote_timestamp, chat_read_status,
               create_by, create_time, update_by, update_time, remark, del_flag
        from sys_message
        where quote_oid = #{quoteOid} and del_flag = 0
        order by create_time desc
    </select>

</mapper>
