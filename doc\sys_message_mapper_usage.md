# SysMessageMapper 增删改查方法使用指南

## 📋 概述

为SysMessageMapper添加了完整的增删改查XML方法，包括基础CRUD操作和扩展查询功能。

## 🔧 新增方法列表

### 基础增删改查方法

#### 1. 插入操作
```java
// 单条插入
int insertMessage(SysMessage message);

// 批量插入
int batchInsertMessage(List<SysMessage> messageList);
```

#### 2. 查询操作
```java
// 根据ID查询
SysMessage selectMessageById(String messageId);

// 条件查询列表
List<SysMessage> selectMessageList(SysMessage message);
```

#### 3. 更新操作
```java
// 更新消息
int updateMessage(SysMessage message);

// 批量更新已读状态
int batchUpdateReadStatus(String[] messageIds, Integer readStatus);

// 更新聊天消息已读状态
int updateChatReadStatus(String senderId, String receiveTarget);
```

#### 4. 删除操作
```java
// 逻辑删除
int deleteMessageById(String messageId);
int deleteMessageByIds(String[] messageIds);

// 物理删除
int removeMessageById(String messageId);
int removeMessageByIds(String[] messageIds);

// 删除聊天记录
int deleteChatMessages(String senderId, String receiveTarget);
```

### 扩展查询方法

```java
// 统计查询
int countMessagesByType(String type);
int countUnreadChatMessages(String userId);

// 条件查询
List<SysMessage> selectMessagesBySender(Long senderId);
List<SysMessage> selectMessagesByReceiver(String receiveTarget);
List<SysMessage> selectChatMessages(String senderId, String receiveTarget);
List<SysMessage> selectMessagesByQuoteId(String quoteOid);

// 特殊查询
SysMessage selectLatestChatMessage(String senderId, String receiveTarget);
int cleanExpiredChatImages(int days);
```

## 💡 使用示例

### 1. 发送聊天消息
```java
@Service
public class ChatService {
    
    @Autowired
    private SysMessageMapper messageMapper;
    
    public void sendChatMessage(String content, String senderId, String receiveTarget) {
        SysMessage message = new SysMessage();
        message.setMessageId(IdUtils.fastUUID());
        message.setContent(content);
        message.setType("CHAT");
        message.setSenderId(Long.valueOf(senderId));
        message.setReceiveTarget(receiveTarget);
        message.setReceiveScope("USER");
        message.setChatReadStatus(0);
        message.setCreateTime(new Date());
        
        // 插入消息
        messageMapper.insertMessage(message);
    }
}
```

### 2. 获取聊天记录
```java
public List<SysMessage> getChatHistory(String userId1, String userId2) {
    return messageMapper.selectChatMessages(userId1, userId2);
}
```

### 3. 标记消息已读
```java
public void markMessagesAsRead(String[] messageIds) {
    messageMapper.batchUpdateReadStatus(messageIds, 1);
}

public void markChatAsRead(String senderId, String receiveTarget) {
    messageMapper.updateChatReadStatus(senderId, receiveTarget);
}
```

### 4. 批量操作示例
```java
public void batchSendNotifications(List<SysMessage> notifications) {
    // 批量插入通知消息
    messageMapper.batchInsertMessage(notifications);
}

public void cleanupOldMessages(String[] messageIds) {
    // 批量删除过期消息
    messageMapper.deleteMessageByIds(messageIds);
}
```

### 5. 统计查询示例
```java
public int getUnreadChatCount(String userId) {
    return messageMapper.countUnreadChatMessages(userId);
}

public int getSystemMessageCount() {
    return messageMapper.countMessagesByType("SYSTEM");
}
```

### 6. 条件查询示例
```java
public List<SysMessage> searchMessages(String keyword, String type) {
    SysMessage condition = new SysMessage();
    condition.setContent(keyword);
    condition.setType(type);
    
    return messageMapper.selectMessageList(condition);
}
```

### 7. 清理过期数据
```java
@Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
public void cleanExpiredChatImages() {
    // 清理3天前的聊天图片
    int count = messageMapper.cleanExpiredChatImages(3);
    log.info("清理过期聊天图片 {} 条", count);
}
```

## 🔍 XML方法特性

### 1. 动态SQL支持
- 所有插入和更新方法都支持动态字段
- 查询方法支持多条件组合
- 时间范围查询支持

### 2. 安全性
- 使用参数化查询防止SQL注入
- 逻辑删除保护数据安全
- 字段验证和空值处理

### 3. 性能优化
- 批量操作支持
- 索引友好的查询条件
- 分页查询支持

### 4. 扩展性
- 支持自定义查询条件
- 灵活的排序方式
- 易于扩展新字段

## 📊 方法对应关系

| 功能 | Mapper方法 | XML方法ID | 说明 |
|------|------------|-----------|------|
| 插入 | insertMessage | insertMessage | 单条插入 |
| 批量插入 | batchInsertMessage | batchInsertMessage | 批量插入 |
| 查询 | selectMessageById | selectMessageById | 根据ID查询 |
| 列表查询 | selectMessageList | selectMessageList | 条件查询 |
| 更新 | updateMessage | updateMessage | 更新消息 |
| 批量更新状态 | batchUpdateReadStatus | batchUpdateReadStatus | 批量更新已读状态 |
| 逻辑删除 | deleteMessageById | deleteMessageById | 单条逻辑删除 |
| 批量逻辑删除 | deleteMessageByIds | deleteMessageByIds | 批量逻辑删除 |
| 物理删除 | removeMessageById | removeMessageById | 单条物理删除 |
| 聊天记录删除 | deleteChatMessages | deleteChatMessages | 删除聊天记录 |

## ⚠️ 注意事项

### 1. 数据安全
- 优先使用逻辑删除（del_flag=1）
- 物理删除需要谨慎使用
- 重要数据建议备份后删除

### 2. 性能考虑
- 批量操作时注意数据量大小
- 大量数据建议分批处理
- 定期清理过期数据

### 3. 事务管理
- 批量操作建议使用事务
- 关键操作需要回滚机制
- 异常处理要完善

### 4. 字段映射
- 确保实体类字段与数据库字段对应
- 注意日期格式和时区问题
- 枚举类型需要正确映射

## 🚀 最佳实践

1. **使用批量操作**：大量数据操作时使用批量方法提高性能
2. **合理使用索引**：查询条件要利用数据库索引
3. **定期清理数据**：设置定时任务清理过期数据
4. **监控性能**：关注慢查询和资源使用情况
5. **异常处理**：完善的异常处理和日志记录

这些方法为消息系统提供了完整的数据操作能力，支持各种业务场景的需求。
