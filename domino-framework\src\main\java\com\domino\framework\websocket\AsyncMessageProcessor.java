package com.domino.framework.websocket;

import com.domino.common.core.domain.model.LoginUser;
import com.domino.common.utils.SecurityUtils;
import com.domino.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


/**
 * 异步消息处理器
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@Slf4j
@Component
public class AsyncMessageProcessor {

    /**
     * websocket 连接成功或失败的时候，同步用户的在线离线状态
     * @param state 状态 1 在线 0 离线 使
     * @param token token
     */
    @Async
    public void syncUserOnlineState(ISysUserService userService, String state, String token) {
        long a = System.currentTimeMillis();
        log.info("开始异步同步用户状态业务");
        //User user = redisSrv.getUserByToken(token);
        LoginUser user = SecurityUtils.getLoginUser();
        if(user != null) {
//            LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
//            updateWrapper.set(User::getOnline, state);
//            updateWrapper.eq(User::getOid, user.getOid());
//            userService.update(updateWrapper);
            //如果离线了，肯定要把他的token清除了
//			if(state.equals(DPConstant.USER_OFFLINE)) {
//				userService.logout(token);
//			}
        }
        long b = System.currentTimeMillis();
        log.info("结束异步同步用户状态业务，耗时:{} ms",b-a);
    }

    //在线用户表
}
